# FinQA Teacher Model Evaluation

This directory contains scripts for evaluating teacher models on the FinQA dataset using zero-shot prompting and specialized evaluation rules.

## Overview

The evaluation pipeline consists of:

1. **Teacher Model Evaluation** (`teacher_finqa.py`) - Runs zero-shot inference on FinQA dataset
2. **Answer Normalization** (`finqa_answer_normalizer.py`) - Applies FinQA-specific evaluation rules
3. **Complete Pipeline** (`finqa_evaluator.py`) - Orchestrates the full evaluation process
4. **Setup Testing** (`test_finqa_setup.py`) - Verifies everything works correctly

## Dataset

- **Source**: `MothMalone/SLMS-KD-Benchmarks` (finqa config)
- **Splits**: train (6,203 samples), validation (871 samples), test (1,147 samples)
- **Note**: We use train and validation splits only (not test as specified)

## FinQA Dataset Structure

Each sample contains:
- `id`: Unique identifier
- `pre_text`: Text before the table
- `post_text`: Text after the table  
- `table`: Financial data table
- `question`: Question to answer
- `answer`: Original answer format
- `final_result`: Ground truth answer
- `program_re`: Program representation
- `gold_inds`: Gold indices

## Evaluation Rules

The evaluation follows specific rules for financial QA:

1. **Format Equivalence**: If ground truth is numerical and format differs but values are same, consider consistent
   - Example: `0.98` vs `98%` → Correct
   
2. **Rounding Tolerance**: If model answer rounds to ground truth, consider consistent  
   - Example: Ground truth `2` vs model answer `1.98` → Correct

3. **Scale Handling**: Handles millions, billions, thousands, percentages, currency formats

## Quick Start

### 1. Test Setup
```bash
python test_finqa_setup.py
```
This verifies dataset loading, answer normalization, and pipeline components.

### 2. Small Test Run (20 samples)
```bash
python teacher_finqa.py --test
# OR
python finqa_evaluator.py --test-only
```

### 3. Full Evaluation
```bash
python teacher_finqa.py --full
# OR  
python finqa_evaluator.py
```

## Script Details

### `teacher_finqa.py`
- Loads FinQA dataset from HuggingFace
- Formats financial documents (pre_text, table, post_text)
- Generates zero-shot predictions using teacher model
- Saves raw results to JSON files
- Includes extensive debug logging

**Usage:**
```bash
python teacher_finqa.py                # Test mode (20 samples)
python teacher_finqa.py --test         # Test mode (20 samples)  
python teacher_finqa.py --full         # Full evaluation
```

### `finqa_answer_normalizer.py`
- Implements FinQA evaluation rules
- Handles numerical format conversions
- Applies rounding tolerance
- Generates detailed evaluation explanations

**Usage:**
```bash
python finqa_answer_normalizer.py results.json [output.csv]
```

### `finqa_evaluator.py`
- Complete evaluation pipeline
- Runs teacher model → normalization → metrics
- Generates comprehensive reports
- Supports test and full modes

**Usage:**
```bash
python finqa_evaluator.py --test-only          # 20 samples
python finqa_evaluator.py --max-samples 100    # Custom limit
python finqa_evaluator.py                      # Full evaluation
python finqa_evaluator.py --normalize-only results.json  # Normalize existing
```

## Output Files

- `finqa_train_results.json` - Raw teacher predictions on training set
- `finqa_validation_results.json` - Raw teacher predictions on validation set  
- `train_finqa_train_normalized.csv` - Normalized training results
- `val_finqa_validation_normalized.csv` - Normalized validation results
- `finqa_evaluation_summary.txt` - Comprehensive evaluation report

## Configuration

### Model Settings
- **Teacher Model**: `llama3.1:8b` (configurable in `teacher_finqa.py`)
- **Temperature**: 0.1 (low for consistent numerical answers)
- **Max Tokens**: 2048
- **Concurrency**: 10 workers (adjustable)

### Evaluation Settings
- **Tolerance**: 0.01 for numerical comparisons
- **Rounding**: Automatic rounding rule application
- **Logging**: Every 5 samples for debugging

## Debug Features

- Progress logging every 10 samples
- Sample details every 5 samples  
- Error tracking and reporting
- Detailed evaluation explanations
- Success/failure rate monitoring

## Example Output

```
=== Running evaluation on train split ===
Using subset of 20 samples for testing
Starting evaluation with 5 workers...
Processing question: What was the total revenue in 2019?...
Generated answer: 1.2 billion
Processed 5/20 samples. Success: 5, Failed: 0
Sample 5 - ID: finqa_train_001
  Question: What was the total revenue in 2019?...
  Ground Truth: 1200000000
  Predicted: 1.2 billion  
  Success: True
--------------------------------------------------
```

## Troubleshooting

### Common Issues

1. **Dataset Loading Error**: Ensure internet connection and HuggingFace access
2. **Model Connection Error**: Verify Ollama is running on localhost:11434
3. **Memory Issues**: Reduce `max_workers` parameter
4. **Timeout Issues**: Increase `max_completion_tokens`

### Debug Mode
Add debug prints by modifying the logging frequency in the scripts.

## Next Steps

1. Run test evaluation to verify setup
2. Analyze sample results and error patterns  
3. Adjust evaluation rules if needed
4. Run full evaluation on complete dataset
5. Compare results across different teacher models
