#!/usr/bin/env python3
"""
Test script to debug a single FinQA sample and see what's happening.
"""

import datasets
import dotenv
from teacher_finqa import predict_answer, format_table, format_text_sequences

# Load environment and dataset
dotenv.load_dotenv('../../.env')
ds = datasets.load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")
train_data = ds['train']

def test_single_sample(index=0):
    """Test a single sample with full debugging."""
    sample = train_data[index]
    
    print("=" * 80)
    print(f"TESTING SAMPLE {index}: {sample['id']}")
    print("=" * 80)
    
    print(f"Question: {sample['question']}")
    print(f"Ground Truth: {sample['final_result']}")
    print()
    
    # Show the raw data
    print("RAW DATA:")
    print(f"Pre-text: {sample.get('pre_text', [])}")
    print(f"Table: {sample.get('table', [])}")
    print(f"Post-text: {sample.get('post_text', [])}")
    print()
    
    # Show formatted data
    pre_text = format_text_sequences(sample.get('pre_text', []))
    post_text = format_text_sequences(sample.get('post_text', []))
    table = format_table(sample.get('table', []))
    
    print("FORMATTED DATA:")
    print("Pre-text:")
    print(pre_text[:500] + "..." if len(pre_text) > 500 else pre_text)
    print()
    print("Table:")
    print(table[:500] + "..." if len(table) > 500 else table)
    print()
    print("Post-text:")
    print(post_text[:500] + "..." if len(post_text) > 500 else post_text)
    print()
    
    # Test prediction
    print("RUNNING PREDICTION...")
    predicted_answer, reasoning = predict_answer(sample, debug=True)
    
    print(f"Predicted Answer: {predicted_answer}")
    print(f"Ground Truth: {sample['final_result']}")
    
    # Manual analysis
    print("\nMANUAL ANALYSIS:")
    print("Look at the question and see if you can find the answer in the data above.")
    print("This will help us understand if the model has the right information.")

def test_multiple_samples():
    """Test first 3 samples to see patterns."""
    for i in range(3):
        test_single_sample(i)
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    print("Testing single FinQA sample to debug the issue...")
    test_single_sample(0)  # Test first sample
    
    print("\n\nWould you like to test more samples? Run with different index numbers.")
