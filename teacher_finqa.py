from openai import OpenAI
import datasets
import dotenv
import os
import warnings
import concurrent.futures
import instructor
from pydantic import BaseModel, Field
from sklearn.metrics import precision_score, recall_score, f1_score, accuracy_score
import numpy as np
import pandas as pd
import json
import re
from typing import List, Dict, Any

warnings.filterwarnings("ignore")

class FinQAAnswer(BaseModel):
    answer: str = Field(
        description="The final numerical or text answer to the financial question. Provide ONLY the answer value, no additional formatting."
    )

# Load environment variables
dotenv.load_dotenv('../../.env')

# Load FinQA dataset
print("Loading FinQA dataset...")
ds = datasets.load_dataset("MothMalone/SLMS-KD-Benchmarks", "finqa")

# Use train and validation splits (not test as specified)
train_data = ds['train']
val_data = ds['validation']

print(f"Loaded {len(train_data)} training samples and {len(val_data)} validation samples.")

DATASET_CONTEXT = """
FinQA is a financial question answering dataset that requires numerical reasoning over financial documents.
Each question is based on financial reports containing tables and text. You need to analyze the provided
financial information and answer the question with precise numerical calculations.
"""

TEACHER_MODEL_ID = "llama3.1:8b"

client = instructor.from_openai(
    OpenAI(
        base_url="http://localhost:11434/v1",
        api_key="ollama",
    ),
    mode=instructor.Mode.JSON
)

def format_table(table_data):
    """Format table data into a readable string."""
    if not table_data or len(table_data) == 0:
        return "No table data provided."
    
    formatted_table = []
    for row in table_data:
        if isinstance(row, list):
            formatted_table.append(" | ".join(str(cell) for cell in row))
        else:
            formatted_table.append(str(row))
    
    return "\n".join(formatted_table)

def format_text_sequences(text_seq):
    """Format text sequences into readable paragraphs."""
    if not text_seq:
        return ""
    if isinstance(text_seq, list):
        return "\n".join(text_seq)
    return str(text_seq)

def predict_answer(finqa_data):
    """Generate zero-shot prediction for a FinQA sample."""
    print(f"Processing question: {finqa_data['question'][:100]}...")

    # Format the input data
    pre_text = format_text_sequences(finqa_data.get('pre_text', []))
    post_text = format_text_sequences(finqa_data.get('post_text', []))
    table = format_table(finqa_data.get('table', []))
    question = finqa_data['question']

    prompt = f"""
    {DATASET_CONTEXT}

    Financial Document Information:

    Pre-text:
    {pre_text}

    Table:
    {table}

    Post-text:
    {post_text}

    Question: {question}

    Please analyze the financial information provided and answer the question.
    Provide ONLY the final answer as a simple value (number, percentage, or short text).
    Do not include explanations, reasoning, or additional formatting in your answer.

    Examples of good answers:
    - 15.2%
    - 1000000
    - 2.5 billion
    - yes
    - no
    """

    try:
        completion = client.chat.completions.create(
            model=TEACHER_MODEL_ID,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            response_model=FinQAAnswer,
            temperature=0.1,  # Lower temperature for more consistent numerical answers
            max_completion_tokens=512,  # Reduced since we only want the answer
            top_p=0.9,
        )

        print(f"Generated answer: {completion.answer}")
        return completion.answer, None  # No reasoning for now to avoid parsing issues

    except Exception as e:
        print(f"Error generating answer: {str(e)}")
        # Try fallback without structured output
        try:
            print("Trying fallback without structured output...")
            fallback_completion = client.chat.completions.create(
                model=TEACHER_MODEL_ID,
                messages=[
                    {
                        "role": "user",
                        "content": prompt + "\n\nProvide only the final answer value:"
                    }
                ],
                temperature=0.1,
                max_completion_tokens=100,
                top_p=0.9,
            )

            answer = fallback_completion.choices[0].message.content.strip()
            print(f"Fallback answer: {answer}")
            return answer, None

        except Exception as e2:
            print(f"Fallback also failed: {str(e2)}")
            return None, None

def process_finqa_sample(sample_data):
    """Process a single FinQA sample and return results."""
    predicted_answer, reasoning = predict_answer(sample_data)

    if predicted_answer is None:
        print(f"Failed to generate answer for sample {sample_data.get('id', 'unknown')}")
        return {
            'id': sample_data.get('id', 'unknown'),
            'question': sample_data['question'],
            'ground_truth': sample_data['final_result'],
            'predicted_answer': None,
            'reasoning': reasoning,
            'success': False
        }

    return {
        'id': sample_data.get('id', 'unknown'),
        'question': sample_data['question'],
        'ground_truth': sample_data['final_result'],
        'predicted_answer': predicted_answer,
        'reasoning': reasoning,
        'success': True
    }

def run_evaluation(data, split_name, max_samples=None, max_workers=10):
    """Run evaluation on a dataset split."""
    print(f"\n=== Running evaluation on {split_name} split ===")
    
    if max_samples:
        data = data.select(range(min(max_samples, len(data))))
        print(f"Using subset of {len(data)} samples for testing")
    
    results = []
    successful_predictions = 0
    failed_predictions = 0
    
    print(f"Starting evaluation with {max_workers} workers...")
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_sample = {executor.submit(process_finqa_sample, sample): i 
                           for i, sample in enumerate(data)}
        
        for i, future in enumerate(concurrent.futures.as_completed(future_to_sample)):
            try:
                result = future.result()
                results.append(result)
                
                if result['success']:
                    successful_predictions += 1
                else:
                    failed_predictions += 1
                
                # Log progress every 10 samples
                if (i + 1) % 10 == 0 or (i + 1) == len(data):
                    print(f"Processed {i + 1}/{len(data)} samples. "
                          f"Success: {successful_predictions}, Failed: {failed_predictions}")
                
                # Log sample details every 5 samples for debugging
                if (i + 1) % 5 == 0:
                    print(f"Sample {i + 1} - ID: {result['id']}")
                    print(f"  Question: {result['question'][:100]}...")
                    print(f"  Ground Truth: {result['ground_truth']}")
                    print(f"  Predicted: {result['predicted_answer']}")
                    print(f"  Success: {result['success']}")
                    print("-" * 50)
                    
            except Exception as e:
                print(f"Error processing sample {i}: {str(e)}")
                failed_predictions += 1
    
    # Save results to file
    results_file = f"finqa_{split_name}_results.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nEvaluation complete!")
    print(f"Total samples: {len(results)}")
    print(f"Successful predictions: {successful_predictions}")
    print(f"Failed predictions: {failed_predictions}")
    print(f"Success rate: {successful_predictions/len(results)*100:.2f}%")
    print(f"Results saved to: {results_file}")
    
    return results

if __name__ == "__main__":
    import sys

    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--full":
        print("Running full evaluation...")
        train_results = run_evaluation(train_data, "train", max_workers=10)
        val_results = run_evaluation(val_data, "validation", max_workers=10)
    elif len(sys.argv) > 1 and sys.argv[1] == "--test":
        print("Running test evaluation on 20 samples...")
        test_results = run_evaluation(train_data, "train_test", max_samples=20, max_workers=5)
    else:
        # Default: Test on small subset first
        print("Starting with small test on 20 samples...")
        print("Use --test for 20 samples, --full for complete evaluation")
        test_results = run_evaluation(train_data, "train_test", max_samples=20, max_workers=5)

        print("\nSmall test completed. To run full evaluation, use:")
        print("python teacher_finqa.py --full")
